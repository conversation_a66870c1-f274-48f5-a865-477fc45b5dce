/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.g3.yop.gateway.filter.protocol.response.yop.v300rc1.ExceptionResult;
import com.yeepay.g3.yop.gateway.filter.protocol.response.yop.v300rc1.exceptions.StatusEnum;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.*;
import io.netty.handler.timeout.ReadTimeoutException;
import io.netty.handler.timeout.WriteTimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * title: Netty异常处理器<br/>
 * description: 统一处理Netty层面的异常，按照异常类型返回标准化的错误响应<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Slf4j
@Component
public class NettyExceptionHandler {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    /**
     * 处理异常并返回标准化的HTTP响应
     *
     * @param ctx       Channel上下文
     * @param throwable 异常
     * @param requestId 请求ID
     */
    public void handleException(ChannelHandlerContext ctx, Throwable throwable, String requestId) {
        try {
            ExceptionResult errorResult = createErrorResult(throwable, requestId);
            FullHttpResponse errorResponse = createErrorResponse(errorResult);
            
            log.error("Request processing failed [{}]: {}", requestId, throwable.getMessage(), throwable);
            
            // 发送错误响应
            ctx.writeAndFlush(errorResponse).addListener(future -> {
                if (!future.isSuccess()) {
                    log.error("Failed to send error response [{}]", requestId, future.cause());
                }
                // 对于严重错误，关闭连接
                if (shouldCloseConnection(throwable)) {
                    ctx.close();
                }
            });
            
        } catch (Exception e) {
            log.error("Error handling exception [{}]", requestId, e);
            // 发送最基本的错误响应
            sendBasicErrorResponse(ctx, requestId);
        }
    }

    /**
     * 根据异常类型创建错误结果
     */
    private ExceptionResult createErrorResult(Throwable throwable, String requestId) {
        ExceptionResult.Builder builder = ExceptionResult.newBuilder().requestId(requestId);
        
        // 根据异常类型设置不同的错误码和消息
        if (throwable instanceof IllegalArgumentException) {
            return builder
                    .code(StatusEnum.ILLEGAL_ARGUMENT)
                    .subCode("netty.illegal.argument")
                    .subMessage("请求参数不合法: " + throwable.getMessage())
                    .build();
                    
        } else if (throwable instanceof ReadTimeoutException) {
            return builder
                    .code(StatusEnum.SERVICE_UNAVAILABLE)
                    .subCode("netty.read.timeout")
                    .subMessage("读取超时")
                    .build();
                    
        } else if (throwable instanceof WriteTimeoutException) {
            return builder
                    .code(StatusEnum.SERVICE_UNAVAILABLE)
                    .subCode("netty.write.timeout")
                    .subMessage("写入超时")
                    .build();
                    
        } else if (throwable instanceof TimeoutException) {
            return builder
                    .code(StatusEnum.SERVICE_UNAVAILABLE)
                    .subCode("netty.request.timeout")
                    .subMessage("请求处理超时")
                    .build();
                    
        } else if (throwable instanceof RejectedExecutionException) {
            return builder
                    .code(StatusEnum.LIMITED)
                    .subCode("netty.thread.pool.rejected")
                    .subMessage("系统繁忙，请稍后重试")
                    .build();
                    
        } else if (throwable instanceof IOException) {
            return builder
                    .code(StatusEnum.SERVICE_UNAVAILABLE)
                    .subCode("netty.io.error")
                    .subMessage("网络IO异常")
                    .build();
                    
        } else if (throwable instanceof OutOfMemoryError) {
            return builder
                    .code(StatusEnum.SERVICE_UNAVAILABLE)
                    .subCode("netty.out.of.memory")
                    .subMessage("系统内存不足")
                    .build();
                    
        } else {
            // 默认内部服务器错误
            return builder
                    .code(StatusEnum.SERVICE_UNAVAILABLE)
                    .subCode("netty.internal.error")
                    .subMessage("内部服务器错误")
                    .build();
        }
    }

    /**
     * 创建错误响应
     */
    private FullHttpResponse createErrorResponse(ExceptionResult errorResult) throws Exception {
        String jsonContent = OBJECT_MAPPER.writeValueAsString(errorResult);
        byte[] contentBytes = jsonContent.getBytes(StandardCharsets.UTF_8);
        
        // 根据错误类型确定HTTP状态码
        HttpResponseStatus httpStatus = getHttpStatus(errorResult);
        
        FullHttpResponse response = new DefaultFullHttpResponse(
                HttpVersion.HTTP_1_1,
                httpStatus,
                Unpooled.wrappedBuffer(contentBytes)
        );
        
        // 设置响应头
        HttpHeaders headers = response.headers();
        headers.set(HttpHeaderNames.CONTENT_TYPE, "application/json; charset=UTF-8");
        headers.set(HttpHeaderNames.CONTENT_LENGTH, contentBytes.length);
        headers.set(HttpHeaderNames.CONNECTION, HttpHeaderValues.CLOSE);
        headers.set(HttpHeaderNames.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        headers.set(HttpHeaderNames.PRAGMA, "no-cache");
        headers.set(HttpHeaderNames.EXPIRES, "0");
        
        return response;
    }

    /**
     * 根据错误结果获取HTTP状态码
     */
    private HttpResponseStatus getHttpStatus(ExceptionResult errorResult) {
        String code = errorResult.getCode();
        
        switch (code) {
            case "40041": // MISS_ARGUMENT
            case "40042": // ILLEGAL_ARGUMENT
                return HttpResponseStatus.BAD_REQUEST;
                
            case "40047": // UNAUTHORIZED
                return HttpResponseStatus.UNAUTHORIZED;
                
            case "40021": // FORBIDDEN
                return HttpResponseStatus.FORBIDDEN;
                
            case "40029": // LIMITED
                return HttpResponseStatus.TOO_MANY_REQUESTS;
                
            case "40020": // SERVICE_UNAVAILABLE
            case "40044": // BUSINESS_ERROR
            default:
                return HttpResponseStatus.INTERNAL_SERVER_ERROR;
        }
    }

    /**
     * 判断是否应该关闭连接
     */
    private boolean shouldCloseConnection(Throwable throwable) {
        return throwable instanceof OutOfMemoryError ||
               throwable instanceof IOException ||
               (throwable instanceof RuntimeException && 
                throwable.getMessage() != null && 
                throwable.getMessage().contains("connection"));
    }

    /**
     * 发送最基本的错误响应（当异常处理本身失败时使用）
     */
    private void sendBasicErrorResponse(ChannelHandlerContext ctx, String requestId) {
        try {
            String basicError = "{\"requestId\":\"" + requestId + 
                              "\",\"code\":\"40020\",\"message\":\"服务不可用\"," +
                              "\"subCode\":\"netty.handler.error\",\"subMessage\":\"异常处理失败\"}";
            
            FullHttpResponse response = new DefaultFullHttpResponse(
                    HttpVersion.HTTP_1_1,
                    HttpResponseStatus.INTERNAL_SERVER_ERROR,
                    Unpooled.wrappedBuffer(basicError.getBytes(StandardCharsets.UTF_8))
            );
            
            response.headers().set(HttpHeaderNames.CONTENT_TYPE, "application/json; charset=UTF-8");
            response.headers().set(HttpHeaderNames.CONTENT_LENGTH, response.content().readableBytes());
            response.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.CLOSE);
            
            ctx.writeAndFlush(response).addListener(future -> ctx.close());
            
        } catch (Exception e) {
            log.error("Failed to send basic error response [{}]", requestId, e);
            ctx.close();
        }
    }
}
