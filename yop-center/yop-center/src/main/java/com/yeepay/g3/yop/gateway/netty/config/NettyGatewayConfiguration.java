/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.config;

import com.yeepay.g3.yop.gateway.netty.backpressure.BackpressureController;
import com.yeepay.g3.yop.gateway.netty.exception.NettyExceptionHandler;
import com.yeepay.g3.yop.gateway.netty.handler.YopGatewayHandler;
import com.yeepay.g3.yop.gateway.netty.metrics.NettyServerMetrics;
import com.yeepay.g3.yop.gateway.netty.resource.NettyResourceManager;
import com.yeepay.g3.yop.gateway.netty.server.YopGatewayServer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * title: Netty网关配置<br/>
 * description: Netty网关相关Bean的配置类<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Configuration
public class NettyGatewayConfiguration {

    @Bean
    public NettyServerConfig nettyServerConfig() {
        return new NettyServerConfig();
    }

    @Bean
    public ExecutorService businessExecutor(NettyServerConfig config) {
        return new ThreadPoolExecutor(
                config.getBusinessThreads(),
                config.getBusinessThreads(),
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(config.getBusinessQueueSize()),
                r -> {
                    Thread t = new Thread(r, "yop-business-" + System.currentTimeMillis());
                    t.setDaemon(false);
                    return t;
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Bean
    public NettyServerMetrics nettyServerMetrics() {
        return new NettyServerMetrics();
    }

    @Bean
    public NettyExceptionHandler nettyExceptionHandler() {
        return new NettyExceptionHandler();
    }

    @Bean
    public NettyResourceManager nettyResourceManager() {
        return new NettyResourceManager();
    }

    @Bean
    public BackpressureController backpressureController(NettyServerConfig config) {
        return new BackpressureController();
    }

    @Bean
    public YopGatewayHandler yopGatewayHandler(ExecutorService businessExecutor,
                                               NettyServerMetrics metrics,
                                               NettyExceptionHandler exceptionHandler,
                                               NettyResourceManager resourceManager,
                                               BackpressureController backpressureController) {
        YopGatewayHandler handler = new YopGatewayHandler();
        handler.setBusinessExecutor(businessExecutor);
        handler.setMetrics(metrics);
        handler.setExceptionHandler(exceptionHandler);
        handler.setResourceManager(resourceManager);
        handler.setBackpressureController(backpressureController);
        return handler;
    }

    @Bean
    public YopGatewayServer yopGatewayServer(NettyServerConfig config,
                                             YopGatewayHandler gatewayHandler) {
        YopGatewayServer server = new YopGatewayServer();
        server.setConfig(config);
        server.setGatewayHandler(gatewayHandler);
        return server;
    }

}