/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.health;

import com.yeepay.g3.yop.gateway.netty.backpressure.BackpressureController;
import com.yeepay.g3.yop.gateway.netty.config.DynamicThreadPoolConfig;
import com.yeepay.g3.yop.gateway.netty.metrics.NettyServerMetrics;
import com.yeepay.g3.yop.gateway.netty.server.YopGatewayServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * title: 健康检查控制器<br/>
 * description: 提供系统健康状态检查和监控信息<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@RestController
@RequestMapping("/actuator")
public class HealthController {

    @Autowired(required = false)
    private YopGatewayServer gatewayServer;

    @Autowired(required = false)
    private NettyServerMetrics metrics;

    @Autowired(required = false)
    private BackpressureController backpressureController;

    @Autowired(required = false)
    private ExecutorService businessExecutor;

    /**
     * 基础健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 检查网关服务器状态
            boolean serverRunning = gatewayServer != null && gatewayServer.isRunning();
            health.put("status", serverRunning ? "UP" : "DOWN");
            health.put("timestamp", System.currentTimeMillis());
            
            // 添加组件状态
            Map<String, Object> components = new HashMap<>();
            components.put("nettyServer", serverRunning ? "UP" : "DOWN");
            components.put("threadPool", businessExecutor != null ? "UP" : "DOWN");
            components.put("backpressure", backpressureController != null ? "UP" : "DOWN");
            components.put("metrics", metrics != null ? "UP" : "DOWN");
            
            health.put("components", components);
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(503).body(health);
        }
    }

    /**
     * 详细监控信息
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> metrics() {
        Map<String, Object> metricsData = new HashMap<>();
        
        try {
            // 服务器基本信息
            if (gatewayServer != null) {
                Map<String, Object> serverInfo = new HashMap<>();
                serverInfo.put("running", gatewayServer.isRunning());
                if (gatewayServer.getConfig() != null) {
                    serverInfo.put("port", gatewayServer.getConfig().getPort());
                    serverInfo.put("businessThreads", gatewayServer.getConfig().getBusinessThreads());
                    serverInfo.put("workerThreads", gatewayServer.getConfig().getWorkerThreads());
                }
                metricsData.put("server", serverInfo);
            }

            // 请求统计
            if (metrics != null) {
                Map<String, Object> requestStats = new HashMap<>();
                requestStats.put("totalRequests", metrics.getRequestCount());
                requestStats.put("successRequests", metrics.getSuccessCount());
                requestStats.put("errorRequests", metrics.getErrorCount());
                requestStats.put("activeConnections", metrics.getActiveConnections());
                metricsData.put("requests", requestStats);
            }

            // 背压控制信息
            if (backpressureController != null) {
                BackpressureController.LoadStats loadStats = backpressureController.getLoadStats();
                Map<String, Object> backpressureInfo = new HashMap<>();
                backpressureInfo.put("activeRequests", loadStats.getActiveRequests());
                backpressureInfo.put("queuedRequests", loadStats.getQueuedRequests());
                backpressureInfo.put("rejectedRequests", loadStats.getRejectedRequests());
                backpressureInfo.put("totalRequests", loadStats.getTotalRequests());
                backpressureInfo.put("overloaded", loadStats.isOverloaded());
                backpressureInfo.put("activeRatio", loadStats.getActiveRatio());
                backpressureInfo.put("queueRatio", loadStats.getQueueRatio());
                metricsData.put("backpressure", backpressureInfo);
            }

            // 线程池信息
            if (businessExecutor != null) {
                DynamicThreadPoolConfig.ThreadPoolStats threadPoolStats = 
                    DynamicThreadPoolConfig.getStats(businessExecutor);
                if (threadPoolStats != null) {
                    Map<String, Object> threadPoolInfo = new HashMap<>();
                    threadPoolInfo.put("corePoolSize", threadPoolStats.getCorePoolSize());
                    threadPoolInfo.put("maximumPoolSize", threadPoolStats.getMaximumPoolSize());
                    threadPoolInfo.put("activeCount", threadPoolStats.getActiveCount());
                    threadPoolInfo.put("poolSize", threadPoolStats.getPoolSize());
                    threadPoolInfo.put("queueSize", threadPoolStats.getQueueSize());
                    threadPoolInfo.put("completedTaskCount", threadPoolStats.getCompletedTaskCount());
                    metricsData.put("threadPool", threadPoolInfo);
                }
            }

            // 系统信息
            Map<String, Object> systemInfo = new HashMap<>();
            Runtime runtime = Runtime.getRuntime();
            systemInfo.put("availableProcessors", runtime.availableProcessors());
            systemInfo.put("totalMemory", runtime.totalMemory());
            systemInfo.put("freeMemory", runtime.freeMemory());
            systemInfo.put("maxMemory", runtime.maxMemory());
            systemInfo.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
            metricsData.put("system", systemInfo);

            metricsData.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(metricsData);
            
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 重置统计信息
     */
    @GetMapping("/metrics/reset")
    public ResponseEntity<Map<String, Object>> resetMetrics() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (metrics != null) {
                metrics.reset();
            }
            
            if (backpressureController != null) {
                backpressureController.resetStats();
            }
            
            response.put("status", "success");
            response.put("message", "Metrics reset successfully");
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> config() {
        Map<String, Object> configData = new HashMap<>();
        
        try {
            if (gatewayServer != null && gatewayServer.getConfig() != null) {
                var config = gatewayServer.getConfig();
                Map<String, Object> nettyConfig = new HashMap<>();
                nettyConfig.put("port", config.getPort());
                nettyConfig.put("bossThreads", config.getBossThreads());
                nettyConfig.put("workerThreads", config.getWorkerThreads());
                nettyConfig.put("businessThreads", config.getBusinessThreads());
                nettyConfig.put("businessQueueSize", config.getBusinessQueueSize());
                nettyConfig.put("maxContentLength", config.getMaxContentLength());
                nettyConfig.put("readTimeoutSeconds", config.getReadTimeoutSeconds());
                nettyConfig.put("writeTimeoutSeconds", config.getWriteTimeoutSeconds());
                nettyConfig.put("epollEnabled", config.isEpollEnabled());
                nettyConfig.put("compressionEnabled", config.isCompressionEnabled());
                configData.put("netty", nettyConfig);
            }
            
            configData.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(configData);
            
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
}
