/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * title: 动态线程池配置<br/>
 * description: 提供可动态调整的线程池配置，支持负载自适应<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Slf4j
@Configuration
public class DynamicThreadPoolConfig {

    @Autowired
    private NettyServerConfig config;

    /**
     * 创建动态业务线程池
     */
    @Bean
    public ExecutorService businessExecutor() {
        return new DynamicThreadPoolExecutor(
                config.getBusinessThreads() / 2,           // 核心线程数：配置的一半
                config.getBusinessThreads(),               // 最大线程数：配置值
                60L,                                       // 空闲线程存活时间
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(config.getBusinessQueueSize()),
                new NamedThreadFactory("yop-business"),
                new SmartRejectedExecutionHandler()
        );
    }

    /**
     * 动态线程池执行器
     */
    public static class DynamicThreadPoolExecutor extends ThreadPoolExecutor {
        
        private volatile long lastAdjustTime = System.currentTimeMillis();
        private static final long ADJUST_INTERVAL = 30000; // 30秒调整一次
        
        public DynamicThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                                       TimeUnit unit, BlockingQueue<Runnable> workQueue,
                                       ThreadFactory threadFactory, RejectedExecutionHandler handler) {
            super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
        }

        @Override
        public void execute(Runnable command) {
            // 在执行任务前检查是否需要调整线程池
            adjustPoolSizeIfNeeded();
            super.execute(command);
        }

        /**
         * 根据负载情况动态调整线程池大小
         */
        private void adjustPoolSizeIfNeeded() {
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastAdjustTime < ADJUST_INTERVAL) {
                return;
            }
            
            lastAdjustTime = currentTime;
            
            int activeCount = getActiveCount();
            int coreSize = getCorePoolSize();
            int maxSize = getMaximumPoolSize();
            int queueSize = getQueue().size();
            
            // 负载过高：活跃线程数接近最大值且队列有积压
            if (activeCount >= coreSize * 0.8 && queueSize > 0) {
                int newCoreSize = Math.min(coreSize + 2, maxSize);
                if (newCoreSize > coreSize) {
                    setCorePoolSize(newCoreSize);
                    log.info("Increased core pool size from {} to {} due to high load", coreSize, newCoreSize);
                }
            }
            // 负载较低：活跃线程数很少且队列为空
            else if (activeCount <= coreSize * 0.3 && queueSize == 0) {
                int originalCoreSize = getMaximumPoolSize() / 2; // 原始核心线程数
                int newCoreSize = Math.max(coreSize - 1, originalCoreSize);
                if (newCoreSize < coreSize) {
                    setCorePoolSize(newCoreSize);
                    log.info("Decreased core pool size from {} to {} due to low load", coreSize, newCoreSize);
                }
            }
        }
    }

    /**
     * 命名线程工厂
     */
    public static class NamedThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;
        private final boolean daemon;

        public NamedThreadFactory(String namePrefix) {
            this(namePrefix, false);
        }

        public NamedThreadFactory(String namePrefix, boolean daemon) {
            this.namePrefix = namePrefix + "-";
            this.daemon = daemon;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            t.setDaemon(daemon);
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }

    /**
     * 智能拒绝策略
     */
    public static class SmartRejectedExecutionHandler implements RejectedExecutionHandler {
        
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            // 记录拒绝信息
            log.warn("Task rejected: active={}, pool={}, queue={}", 
                    executor.getActiveCount(), executor.getPoolSize(), executor.getQueue().size());
            
            // 尝试在调用线程中执行（CallerRunsPolicy的行为）
            if (!executor.isShutdown()) {
                try {
                    r.run();
                } catch (Exception e) {
                    log.error("Error executing rejected task in caller thread", e);
                }
            }
        }
    }

    /**
     * 线程池监控信息
     */
    public static class ThreadPoolStats {
        private final int corePoolSize;
        private final int maximumPoolSize;
        private final int activeCount;
        private final int poolSize;
        private final int queueSize;
        private final long completedTaskCount;

        public ThreadPoolStats(ThreadPoolExecutor executor) {
            this.corePoolSize = executor.getCorePoolSize();
            this.maximumPoolSize = executor.getMaximumPoolSize();
            this.activeCount = executor.getActiveCount();
            this.poolSize = executor.getPoolSize();
            this.queueSize = executor.getQueue().size();
            this.completedTaskCount = executor.getCompletedTaskCount();
        }

        // Getters
        public int getCorePoolSize() { return corePoolSize; }
        public int getMaximumPoolSize() { return maximumPoolSize; }
        public int getActiveCount() { return activeCount; }
        public int getPoolSize() { return poolSize; }
        public int getQueueSize() { return queueSize; }
        public long getCompletedTaskCount() { return completedTaskCount; }

        @Override
        public String toString() {
            return String.format("ThreadPoolStats{core=%d, max=%d, active=%d, pool=%d, queue=%d, completed=%d}",
                    corePoolSize, maximumPoolSize, activeCount, poolSize, queueSize, completedTaskCount);
        }
    }

    /**
     * 获取线程池统计信息
     */
    public static ThreadPoolStats getStats(ExecutorService executor) {
        if (executor instanceof ThreadPoolExecutor) {
            return new ThreadPoolStats((ThreadPoolExecutor) executor);
        }
        return null;
    }
}
