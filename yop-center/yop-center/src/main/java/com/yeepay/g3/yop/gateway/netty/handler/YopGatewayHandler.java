/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.handler;

import com.yeepay.boot.components.utils.UUIDExt;
import com.yeepay.g3.yop.gateway.netty.backpressure.BackpressureController;
import com.yeepay.g3.yop.gateway.netty.exception.NettyExceptionHandler;
import com.yeepay.g3.yop.gateway.netty.metrics.NettyServerMetrics;
import com.yeepay.g3.yop.gateway.netty.resource.NettyResourceManager;
import com.yeepay.g3.yop.gateway.netty.server.NettyServerWebExchange;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * title: YOP网关核心处理器<br/>
 * description: 处理HTTP请求的核心业务逻辑，将请求委托给过滤器链处理<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Slf4j
@Component
public class YopGatewayHandler extends SimpleChannelInboundHandler<FullHttpRequest> {

    @Autowired
    private ExecutorService businessExecutor;

    @Autowired
    private NettyExceptionHandler exceptionHandler;

    @Autowired
    private NettyResourceManager resourceManager;

    @Autowired
    private BackpressureController backpressureController;

    @Autowired
    private NettyServerMetrics metrics;

    // Setter methods for configuration injection
    public void setBusinessExecutor(ExecutorService businessExecutor) {
        this.businessExecutor = businessExecutor;
    }

    public void setExceptionHandler(NettyExceptionHandler exceptionHandler) {
        this.exceptionHandler = exceptionHandler;
    }

    public void setResourceManager(NettyResourceManager resourceManager) {
        this.resourceManager = resourceManager;
    }

    public void setBackpressureController(BackpressureController backpressureController) {
        this.backpressureController = backpressureController;
    }

    public void setMetrics(NettyServerMetrics metrics) {
        this.metrics = metrics;
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, FullHttpRequest request) {
        // 生成请求ID
        String requestId = UUIDExt.compressV4UUID();

        // 获取远程地址
        String remoteAddress = ctx.channel().remoteAddress().toString();

        // 记录请求开始
        log.info("Received request: {} {} from {} [{}]",
                request.method(), request.uri(), remoteAddress, requestId);

        // 更新指标
        if (metrics != null) {
            metrics.incrementRequestCount();
        }

        // 背压控制检查
        if (backpressureController != null && !backpressureController.tryAcquire(requestId)) {
            log.warn("Request rejected by backpressure controller [{}]", requestId);
            if (exceptionHandler != null) {
                exceptionHandler.handleException(ctx,
                    new RuntimeException("System overloaded, please retry later"), requestId);
            }
            resourceManager.safeReleaseRequest(request, requestId);
            return;
        }

        // 使用资源守护者确保资源正确释放
        try (NettyResourceManager.ResourceGuard guard = resourceManager.createRequestGuard(request, requestId)) {

            // 创建ServerWebExchange
            NettyServerWebExchange exchange = new NettyServerWebExchange(
                    request, ctx, requestId, remoteAddress, businessExecutor);

            // 异步处理请求
            CompletableFuture<FullHttpResponse> responseFuture = exchange.getResponseFuture();

            // 设置响应完成回调
            responseFuture.whenComplete((response, throwable) -> {
                try {
                    if (throwable != null) {
                        log.error("Request processing failed [{}]", requestId, throwable);
                        if (exceptionHandler != null) {
                            exceptionHandler.handleException(ctx, throwable, requestId);
                        } else {
                            handleException(ctx, throwable, requestId);
                        }
                        if (metrics != null) {
                            metrics.incrementErrorCount();
                        }
                    } else {
                        log.info("Request processed successfully [{}]", requestId);
                        ctx.writeAndFlush(response);
                        if (metrics != null) {
                            metrics.incrementSuccessCount();
                        }
                    }
                } finally {
                    // 释放背压控制许可
                    if (backpressureController != null) {
                        backpressureController.release(requestId);
                    }
                }
            });

            // 在业务线程池中处理请求
            businessExecutor.submit(() -> {
                try {
                    processRequest(exchange);
                } catch (Exception e) {
                    log.error("Error processing request [{}]", requestId, e);
                    exchange.completeExceptionally(e);
                }
            });

        } catch (Exception e) {
            log.error("Error in request processing setup [{}]", requestId, e);
            if (exceptionHandler != null) {
                exceptionHandler.handleException(ctx, e, requestId);
            } else {
                handleException(ctx, e, requestId);
            }
            // 释放背压控制许可
            if (backpressureController != null) {
                backpressureController.release(requestId);
            }
        }
    }

    /**
     * 处理请求
     */
    private void processRequest(NettyServerWebExchange exchange) {
        try {
            // TODO: 这里将集成现有的过滤器链处理逻辑
            // 暂时返回简单响应
            FullHttpResponse response = createSimpleResponse();
            exchange.completeResponse(response);
        } catch (Exception e) {
            exchange.completeExceptionally(e);
        }
    }

    /**
     * 创建简单响应
     */
    private FullHttpResponse createSimpleResponse() {
        String content = "{\"code\":\"SUCCESS\",\"message\":\"YOP Gateway is running\"}";
        FullHttpResponse response = new DefaultFullHttpResponse(
                HttpVersion.HTTP_1_1, 
                HttpResponseStatus.OK,
                Unpooled.wrappedBuffer(content.getBytes())
        );
        
        response.headers().set(HttpHeaderNames.CONTENT_TYPE, "application/json; charset=UTF-8");
        response.headers().set(HttpHeaderNames.CONTENT_LENGTH, response.content().readableBytes());
        response.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.KEEP_ALIVE);
        
        return response;
    }

    /**
     * 处理异常（回退方法，当NettyExceptionHandler不可用时使用）
     */
    private void handleException(ChannelHandlerContext ctx, Throwable throwable, String requestId) {
        log.error("Fallback exception handling for request [{}]", requestId, throwable);
        try {
            String errorContent = "{\"requestId\":\"" + requestId +
                                "\",\"code\":\"40020\",\"message\":\"服务不可用\"," +
                                "\"subCode\":\"netty.fallback.error\",\"subMessage\":\"系统异常\"}";
            FullHttpResponse errorResponse = new DefaultFullHttpResponse(
                    HttpVersion.HTTP_1_1,
                    HttpResponseStatus.INTERNAL_SERVER_ERROR,
                    Unpooled.wrappedBuffer(errorContent.getBytes())
            );

            errorResponse.headers().set(HttpHeaderNames.CONTENT_TYPE, "application/json; charset=UTF-8");
            errorResponse.headers().set(HttpHeaderNames.CONTENT_LENGTH, errorResponse.content().readableBytes());
            errorResponse.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.CLOSE);

            ctx.writeAndFlush(errorResponse).addListener(future -> ctx.close());
        } catch (Exception e) {
            log.error("Error sending fallback error response [{}]", requestId, e);
            ctx.close();
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        String requestId = UUIDExt.compressV4UUID();
        log.error("Channel exception caught from {} [{}]", ctx.channel().remoteAddress(), requestId, cause);

        // 使用统一的异常处理器
        if (exceptionHandler != null) {
            exceptionHandler.handleException(ctx, cause, requestId);
        } else {
            handleException(ctx, cause, requestId);
        }
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        log.debug("Channel active: {}", ctx.channel().remoteAddress());
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        log.debug("Channel inactive: {}", ctx.channel().remoteAddress());
        super.channelInactive(ctx);
    }

}