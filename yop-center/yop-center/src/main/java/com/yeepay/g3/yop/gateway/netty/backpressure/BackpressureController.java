/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.backpressure;

import com.yeepay.g3.yop.gateway.netty.config.NettyServerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * title: 背压控制器<br/>
 * description: 实现系统负载控制和背压机制，防止系统过载<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Slf4j
@Component
public class BackpressureController {

    @Autowired
    private NettyServerConfig config;

    // 当前活跃请求数
    private final AtomicLong activeRequests = new AtomicLong(0);
    
    // 被拒绝的请求数
    private final LongAdder rejectedRequests = new LongAdder();
    
    // 队列中的请求数
    private final AtomicLong queuedRequests = new AtomicLong(0);
    
    // 总处理请求数
    private final LongAdder totalRequests = new LongAdder();
    
    // 配置参数
    private volatile long maxActiveRequests;
    private volatile long maxQueuedRequests;
    private volatile double overloadThreshold;
    private volatile boolean enableBackpressure;

    @PostConstruct
    public void init() {
        // 基于配置初始化参数
        this.maxActiveRequests = config.getBusinessThreads() * 2L; // 业务线程数的2倍
        this.maxQueuedRequests = config.getBusinessQueueSize();
        this.overloadThreshold = 0.8; // 80%负载阈值
        this.enableBackpressure = true;
        
        log.info("BackpressureController initialized - maxActive: {}, maxQueued: {}, threshold: {}", 
                maxActiveRequests, maxQueuedRequests, overloadThreshold);
    }

    /**
     * 尝试获取处理许可
     *
     * @param requestId 请求ID
     * @return 如果允许处理返回true，否则返回false
     */
    public boolean tryAcquire(String requestId) {
        if (!enableBackpressure) {
            activeRequests.incrementAndGet();
            totalRequests.increment();
            return true;
        }

        totalRequests.increment();
        
        // 检查活跃请求数限制
        long currentActive = activeRequests.get();
        if (currentActive >= maxActiveRequests) {
            rejectedRequests.increment();
            log.warn("Request rejected due to active limit [{}]: current={}, max={}", 
                    requestId, currentActive, maxActiveRequests);
            return false;
        }

        // 检查队列限制
        long currentQueued = queuedRequests.get();
        if (currentQueued >= maxQueuedRequests) {
            rejectedRequests.increment();
            log.warn("Request rejected due to queue limit [{}]: current={}, max={}", 
                    requestId, currentQueued, maxQueuedRequests);
            return false;
        }

        // 检查系统负载
        if (isSystemOverloaded()) {
            rejectedRequests.increment();
            log.warn("Request rejected due to system overload [{}]: active={}, queued={}", 
                    requestId, currentActive, currentQueued);
            return false;
        }

        // 获取许可成功
        activeRequests.incrementAndGet();
        log.debug("Request acquired processing permit [{}]: active={}", requestId, currentActive + 1);
        return true;
    }

    /**
     * 释放处理许可
     *
     * @param requestId 请求ID
     */
    public void release(String requestId) {
        long currentActive = activeRequests.decrementAndGet();
        log.debug("Request released processing permit [{}]: active={}", requestId, currentActive);
    }

    /**
     * 请求进入队列
     *
     * @param requestId 请求ID
     * @return 如果允许入队返回true，否则返回false
     */
    public boolean enterQueue(String requestId) {
        if (!enableBackpressure) {
            queuedRequests.incrementAndGet();
            return true;
        }

        long currentQueued = queuedRequests.get();
        if (currentQueued >= maxQueuedRequests) {
            rejectedRequests.increment();
            log.warn("Request rejected from queue [{}]: current={}, max={}", 
                    requestId, currentQueued, maxQueuedRequests);
            return false;
        }

        queuedRequests.incrementAndGet();
        log.debug("Request entered queue [{}]: queued={}", requestId, currentQueued + 1);
        return true;
    }

    /**
     * 请求离开队列
     *
     * @param requestId 请求ID
     */
    public void leaveQueue(String requestId) {
        long currentQueued = queuedRequests.decrementAndGet();
        log.debug("Request left queue [{}]: queued={}", requestId, currentQueued);
    }

    /**
     * 检查系统是否过载
     *
     * @return 如果系统过载返回true
     */
    public boolean isSystemOverloaded() {
        if (!enableBackpressure) {
            return false;
        }

        double activeRatio = (double) activeRequests.get() / maxActiveRequests;
        double queueRatio = (double) queuedRequests.get() / maxQueuedRequests;
        
        return activeRatio >= overloadThreshold || queueRatio >= overloadThreshold;
    }

    /**
     * 获取系统负载统计
     *
     * @return 负载统计信息
     */
    public LoadStats getLoadStats() {
        return LoadStats.builder()
                .activeRequests(activeRequests.get())
                .queuedRequests(queuedRequests.get())
                .rejectedRequests(rejectedRequests.sum())
                .totalRequests(totalRequests.sum())
                .maxActiveRequests(maxActiveRequests)
                .maxQueuedRequests(maxQueuedRequests)
                .overloadThreshold(overloadThreshold)
                .overloaded(isSystemOverloaded())
                .activeRatio((double) activeRequests.get() / maxActiveRequests)
                .queueRatio((double) queuedRequests.get() / maxQueuedRequests)
                .build();
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        rejectedRequests.reset();
        totalRequests.reset();
        log.info("BackpressureController stats reset");
    }

    /**
     * 动态调整配置
     */
    public void updateConfig(long maxActive, long maxQueued, double threshold, boolean enabled) {
        this.maxActiveRequests = maxActive;
        this.maxQueuedRequests = maxQueued;
        this.overloadThreshold = threshold;
        this.enableBackpressure = enabled;
        
        log.info("BackpressureController config updated - maxActive: {}, maxQueued: {}, threshold: {}, enabled: {}", 
                maxActive, maxQueued, threshold, enabled);
    }

    /**
     * 负载统计信息
     */
    public static class LoadStats {
        private final long activeRequests;
        private final long queuedRequests;
        private final long rejectedRequests;
        private final long totalRequests;
        private final long maxActiveRequests;
        private final long maxQueuedRequests;
        private final double overloadThreshold;
        private final boolean overloaded;
        private final double activeRatio;
        private final double queueRatio;

        private LoadStats(Builder builder) {
            this.activeRequests = builder.activeRequests;
            this.queuedRequests = builder.queuedRequests;
            this.rejectedRequests = builder.rejectedRequests;
            this.totalRequests = builder.totalRequests;
            this.maxActiveRequests = builder.maxActiveRequests;
            this.maxQueuedRequests = builder.maxQueuedRequests;
            this.overloadThreshold = builder.overloadThreshold;
            this.overloaded = builder.overloaded;
            this.activeRatio = builder.activeRatio;
            this.queueRatio = builder.queueRatio;
        }

        public static Builder builder() {
            return new Builder();
        }

        // Getters
        public long getActiveRequests() { return activeRequests; }
        public long getQueuedRequests() { return queuedRequests; }
        public long getRejectedRequests() { return rejectedRequests; }
        public long getTotalRequests() { return totalRequests; }
        public long getMaxActiveRequests() { return maxActiveRequests; }
        public long getMaxQueuedRequests() { return maxQueuedRequests; }
        public double getOverloadThreshold() { return overloadThreshold; }
        public boolean isOverloaded() { return overloaded; }
        public double getActiveRatio() { return activeRatio; }
        public double getQueueRatio() { return queueRatio; }

        public static class Builder {
            private long activeRequests;
            private long queuedRequests;
            private long rejectedRequests;
            private long totalRequests;
            private long maxActiveRequests;
            private long maxQueuedRequests;
            private double overloadThreshold;
            private boolean overloaded;
            private double activeRatio;
            private double queueRatio;

            public Builder activeRequests(long activeRequests) { this.activeRequests = activeRequests; return this; }
            public Builder queuedRequests(long queuedRequests) { this.queuedRequests = queuedRequests; return this; }
            public Builder rejectedRequests(long rejectedRequests) { this.rejectedRequests = rejectedRequests; return this; }
            public Builder totalRequests(long totalRequests) { this.totalRequests = totalRequests; return this; }
            public Builder maxActiveRequests(long maxActiveRequests) { this.maxActiveRequests = maxActiveRequests; return this; }
            public Builder maxQueuedRequests(long maxQueuedRequests) { this.maxQueuedRequests = maxQueuedRequests; return this; }
            public Builder overloadThreshold(double overloadThreshold) { this.overloadThreshold = overloadThreshold; return this; }
            public Builder overloaded(boolean overloaded) { this.overloaded = overloaded; return this; }
            public Builder activeRatio(double activeRatio) { this.activeRatio = activeRatio; return this; }
            public Builder queueRatio(double queueRatio) { this.queueRatio = queueRatio; return this; }

            public LoadStats build() {
                return new LoadStats(this);
            }
        }
    }
}
