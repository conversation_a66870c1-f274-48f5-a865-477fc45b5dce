/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.resource;

import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.FullHttpResponse;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * title: Netty资源管理器<br/>
 * description: 安全管理Netty资源的释放，防止内存泄漏<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Slf4j
@Component
public class NettyResourceManager {

    /**
     * 安全释放HTTP请求资源
     *
     * @param request   HTTP请求
     * @param requestId 请求ID，用于日志记录
     */
    public void safeReleaseRequest(FullHttpRequest request, String requestId) {
        if (request == null) {
            return;
        }
        
        try {
            int refCnt = request.refCnt();
            if (refCnt > 0) {
                boolean released = ReferenceCountUtil.release(request);
                if (released) {
                    log.debug("Successfully released request resource [{}], refCnt was: {}", requestId, refCnt);
                } else {
                    log.warn("Failed to release request resource [{}], refCnt: {}", requestId, refCnt);
                }
            } else {
                log.debug("Request resource already released [{}], refCnt: {}", requestId, refCnt);
            }
        } catch (Exception e) {
            log.error("Error releasing request resource [{}]", requestId, e);
        }
    }

    /**
     * 安全释放HTTP响应资源
     *
     * @param response  HTTP响应
     * @param requestId 请求ID，用于日志记录
     */
    public void safeReleaseResponse(FullHttpResponse response, String requestId) {
        if (response == null) {
            return;
        }
        
        try {
            int refCnt = response.refCnt();
            if (refCnt > 0) {
                boolean released = ReferenceCountUtil.release(response);
                if (released) {
                    log.debug("Successfully released response resource [{}], refCnt was: {}", requestId, refCnt);
                } else {
                    log.warn("Failed to release response resource [{}], refCnt: {}", requestId, refCnt);
                }
            } else {
                log.debug("Response resource already released [{}], refCnt: {}", requestId, refCnt);
            }
        } catch (Exception e) {
            log.error("Error releasing response resource [{}]", requestId, e);
        }
    }

    /**
     * 创建资源守护者，确保资源在作用域结束时被释放
     *
     * @param request   HTTP请求
     * @param requestId 请求ID
     * @return 资源守护者
     */
    public ResourceGuard createRequestGuard(FullHttpRequest request, String requestId) {
        return new ResourceGuard(request, requestId, this);
    }

    /**
     * 资源守护者，实现自动资源管理
     */
    public static class ResourceGuard implements AutoCloseable {
        private final FullHttpRequest request;
        private final String requestId;
        private final NettyResourceManager resourceManager;
        private final AtomicBoolean released = new AtomicBoolean(false);

        public ResourceGuard(FullHttpRequest request, String requestId, NettyResourceManager resourceManager) {
            this.request = request;
            this.requestId = requestId;
            this.resourceManager = resourceManager;
        }

        /**
         * 手动释放资源
         */
        public void release() {
            if (released.compareAndSet(false, true)) {
                resourceManager.safeReleaseRequest(request, requestId);
            }
        }

        /**
         * 自动释放资源（try-with-resources支持）
         */
        @Override
        public void close() {
            release();
        }

        /**
         * 检查资源是否已释放
         */
        public boolean isReleased() {
            return released.get();
        }

        /**
         * 获取请求对象
         */
        public FullHttpRequest getRequest() {
            return request;
        }

        /**
         * 获取请求ID
         */
        public String getRequestId() {
            return requestId;
        }
    }

    /**
     * 批量释放资源
     *
     * @param objects   要释放的对象数组
     * @param requestId 请求ID，用于日志记录
     */
    public void safeReleaseAll(Object[] objects, String requestId) {
        if (objects == null || objects.length == 0) {
            return;
        }
        
        for (int i = 0; i < objects.length; i++) {
            Object obj = objects[i];
            if (obj == null) {
                continue;
            }
            
            try {
                boolean released = ReferenceCountUtil.release(obj);
                log.debug("Released object[{}] for request [{}]: {}", i, requestId, released);
            } catch (Exception e) {
                log.error("Error releasing object[{}] for request [{}]", i, requestId, e);
            }
        }
    }

    /**
     * 检查对象是否需要释放
     *
     * @param obj 要检查的对象
     * @return 如果对象需要释放返回true
     */
    public boolean needsRelease(Object obj) {
        if (obj == null) {
            return false;
        }
        
        try {
            return ReferenceCountUtil.refCnt(obj) > 0;
        } catch (Exception e) {
            // 如果无法获取引用计数，假设不需要释放
            return false;
        }
    }

    /**
     * 获取对象的引用计数
     *
     * @param obj       要检查的对象
     * @param requestId 请求ID，用于日志记录
     * @return 引用计数，如果无法获取则返回-1
     */
    public int getRefCount(Object obj, String requestId) {
        if (obj == null) {
            return -1;
        }
        
        try {
            return ReferenceCountUtil.refCnt(obj);
        } catch (Exception e) {
            log.warn("Unable to get ref count for request [{}]", requestId, e);
            return -1;
        }
    }
}
